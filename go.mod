module git.woa.com/msp/urp-api

go 1.22

toolchain go1.22.0

require (
	git.code.oa.com/rainbow/golang-sdk v0.5.4
	git.code.oa.com/rainbow/proto v1.94.0
	git.woa.com/msp/common-sdk v0.0.0-20250414073231-88b1ffbb76b6
	git.woa.com/msp/console-api v1.0.0-realease.0.20231108032104-8f93ea619c19
	git.woa.com/msp/errorcode v0.1.2
	git.woa.com/msp/msp-cli v1.1.3-0.20250515030903-9abee0e94cc5
	git.woa.com/msp/msp-cli/scanner/aliyun v0.0.0-20250515030903-9abee0e94cc5
	git.woa.com/msp/msp-cli/scanner/aws v0.0.0-20250515030903-9abee0e94cc5
	git.woa.com/msp/msp-cli/scanner/huaweicloud v0.0.0-20250515030903-9abee0e94cc5
	git.woa.com/msp/utils v0.5.7-0.20250512092951-d2925e5887ae
	git.woa.com/ssmsdk/ssm-sdk-golang/rotated_credential v1.0.4-0.20230223074258-f226b607dc6c
	github.com/alibabacloud-go/darabonba-openapi/v2 v2.0.8
	github.com/alibabacloud-go/ecs-20140526/v3 v3.0.14
	github.com/alibabacloud-go/ecs-20140526/v4 v4.1.7
	github.com/alibabacloud-go/polardb-20170801/v4 v4.2.1
	github.com/alibabacloud-go/r-kvstore-20150101/v3 v3.3.1
	github.com/alibabacloud-go/rds-20140815/v3 v3.0.223
	github.com/alibabacloud-go/swas-open-20200601 v1.1.1
	github.com/alibabacloud-go/tea v1.2.1
	github.com/alibabacloud-go/tea-utils/v2 v2.0.5
	github.com/aws/aws-sdk-go-v2 v1.36.3
	github.com/aws/aws-sdk-go-v2/service/ec2 v1.170.0
	github.com/aws/aws-sdk-go-v2/service/rds v1.81.5
	github.com/dlclark/regexp2 v1.10.0
	github.com/emirpasic/gods v1.18.1
	github.com/gavv/httpexpect/v2 v2.16.0
	github.com/go-sql-driver/mysql v1.7.1
	github.com/google/uuid v1.6.0
	github.com/hashicorp/go-multierror v1.1.1
	github.com/hibiken/asynq v0.24.1
	github.com/huaweicloud/huaweicloud-sdk-go-v3 v0.1.132
	github.com/mitchellh/mapstructure v1.5.0
	github.com/redis/go-redis/v9 v9.0.3
	github.com/robfig/cron/v3 v3.0.1
	github.com/shakinm/xlsReader v0.9.12
	github.com/stretchr/testify v1.10.0
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.1039
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sts v1.0.392
	github.com/tencentyun/cos-go-sdk-v5 v0.7.57
	github.com/tencentyun/qcloud-cos-sts-sdk v0.0.0-20240926121600-19b39a33db5a
	github.com/thoas/go-funk v0.9.3
	github.com/ucloud/ucloud-sdk-go v0.22.4
	github.com/xrash/smetrics v0.0.0-20240521201337-686a1a2994c1
	github.com/xuri/excelize/v2 v2.7.1
	github.com/zeromicro/go-queue v1.1.8
	github.com/zeromicro/go-zero v1.5.6
	go.opentelemetry.io/otel v1.24.0
	go.uber.org/zap v1.25.0
	gorm.io/datatypes v1.0.7
	gorm.io/driver/mysql v1.4.7
	gorm.io/driver/sqlite v1.4.3
	gorm.io/gorm v1.24.6
)

require (
	git.code.oa.com/polaris/polaris-go v0.12.12 // indirect
	git.code.oa.com/trpc-go/trpc-go v0.8.1 // indirect
	git.woa.com/jce/jce v1.2.0 // indirect
	git.woa.com/msp/admin-api v1.9.1-0.20231108031343-194370a4046b // indirect
	git.woa.com/msp/sdk/huaweicloud-sdk-go-obs v3.24.7-0.20240815105935-52ca44d16fd8+incompatible // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.1.4 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.3 // indirect
	git.woa.com/ssmsdk/ssm-sdk-golang/ssm v1.0.1-0.20221228035846-9b74a38ad5a2 // indirect
	git.woa.com/trpc-go/go_reuseport v1.7.0 // indirect
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/Jeffail/tunny v0.1.4 // indirect
	github.com/TylerBrock/colorjson v0.0.0-20200706003622-8a50f05110d2 // indirect
	github.com/ajg/form v1.5.1 // indirect
	github.com/alibabacloud-go/alb-20200616/v2 v2.0.4 // indirect
	github.com/alibabacloud-go/alibabacloud-gateway-spi v0.0.4 // indirect
	github.com/alibabacloud-go/alidns-20150109/v4 v4.0.7 // indirect
	github.com/alibabacloud-go/alikafka-20190916/v3 v3.6.5 // indirect
	github.com/alibabacloud-go/amqp-open-20191212/v2 v2.0.0 // indirect
	github.com/alibabacloud-go/cdn-20180510 v1.1.2 // indirect
	github.com/alibabacloud-go/cr-20181201/v2 v2.0.1 // indirect
	github.com/alibabacloud-go/cs-20151215/v5 v5.3.2 // indirect
	github.com/alibabacloud-go/darabonba-openapi v0.2.1 // indirect
	github.com/alibabacloud-go/ddoscoo-20200101/v2 v2.0.1 // indirect
	github.com/alibabacloud-go/dds-20151201/v8 v8.0.0 // indirect
	github.com/alibabacloud-go/debug v1.0.0 // indirect
	github.com/alibabacloud-go/ebs-20210730/v2 v2.8.0 // indirect
	github.com/alibabacloud-go/elasticsearch-20170613/v2 v2.0.5 // indirect
	github.com/alibabacloud-go/emr-20210320 v1.0.3 // indirect
	github.com/alibabacloud-go/endpoint-util v1.1.1 // indirect
	github.com/alibabacloud-go/hbase-20190101/v3 v3.0.0 // indirect
	github.com/alibabacloud-go/hitsdb-20170601 v1.0.1 // indirect
	github.com/alibabacloud-go/nas-20170626/v3 v3.0.0 // indirect
	github.com/alibabacloud-go/nlb-20220430 v1.0.11 // indirect
	github.com/alibabacloud-go/ons-20190214/v2 v2.0.1 // indirect
	github.com/alibabacloud-go/openapi-util v0.1.0 // indirect
	github.com/alibabacloud-go/ram-20150501/v2 v2.0.0 // indirect
	github.com/alibabacloud-go/rds-20140815/v7 v7.0.1 // indirect
	github.com/alibabacloud-go/rocketmq-20220801 v1.4.3 // indirect
	github.com/alibabacloud-go/slb-20140515/v4 v4.0.3 // indirect
	github.com/alibabacloud-go/tea-utils v1.4.5 // indirect
	github.com/alibabacloud-go/tea-xml v1.1.3 // indirect
	github.com/alibabacloud-go/vpc-20160428/v2 v2.0.117 // indirect
	github.com/alibabacloud-go/waf-openapi-20190910 v1.1.8 // indirect
	github.com/alibabacloud-go/yundun-bastionhost-20191209 v1.0.3 // indirect
	github.com/aliyun/alibaba-cloud-sdk-go v1.62.509 // indirect
	github.com/aliyun/aliyun-oss-go-sdk v3.0.2+incompatible // indirect
	github.com/aliyun/credentials-go v1.3.1 // indirect
	github.com/andybalholm/brotli v1.0.4 // indirect
	github.com/asaskevich/govalidator v0.0.0-20200907205600-7a23bdc65eef // indirect
	github.com/aws/aws-sdk-go-v2/aws/protocol/eventstream v1.6.10 // indirect
	github.com/aws/aws-sdk-go-v2/config v1.27.26 // indirect
	github.com/aws/aws-sdk-go-v2/credentials v1.17.26 // indirect
	github.com/aws/aws-sdk-go-v2/feature/ec2/imds v1.16.11 // indirect
	github.com/aws/aws-sdk-go-v2/internal/configsources v1.3.34 // indirect
	github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.6.34 // indirect
	github.com/aws/aws-sdk-go-v2/internal/ini v1.8.0 // indirect
	github.com/aws/aws-sdk-go-v2/internal/v4a v1.3.15 // indirect
	github.com/aws/aws-sdk-go-v2/service/apigatewayv2 v1.22.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/cloudfront v1.38.4 // indirect
	github.com/aws/aws-sdk-go-v2/service/cloudtrail v1.48.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/cloudwatch v1.40.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/dynamodb v1.34.8 // indirect
	github.com/aws/aws-sdk-go-v2/service/ecr v1.30.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/efs v1.31.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/eks v1.46.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/elasticache v1.40.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/elasticloadbalancingv2 v1.33.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/emr v1.42.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.11.4 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/checksum v1.3.17 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/endpoint-discovery v1.9.18 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.11.17 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/s3shared v1.17.15 // indirect
	github.com/aws/aws-sdk-go-v2/service/kafka v1.35.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/lambda v1.71.1 // indirect
	github.com/aws/aws-sdk-go-v2/service/mq v1.25.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/opensearch v1.39.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/route53domains v1.16.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/s3 v1.58.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/secretsmanager v1.35.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/sns v1.31.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/sso v1.22.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/ssooidc v1.26.4 // indirect
	github.com/aws/aws-sdk-go-v2/service/sts v1.30.3 // indirect
	github.com/aws/smithy-go v1.22.2 // indirect
	github.com/benbjohnson/clock v1.3.5 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bwmarrin/snowflake v0.3.0 // indirect
	github.com/cenkalti/backoff/v4 v4.2.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/clbanning/mxj v1.8.4 // indirect
	github.com/clbanning/mxj/v2 v2.7.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/fatih/color v1.15.0 // indirect
	github.com/fatih/structs v1.1.0 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/go-logr/logr v1.4.1 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-playground/form v3.1.4+incompatible // indirect
	github.com/gobwas/glob v0.2.3 // indirect
	github.com/golang-jwt/jwt/v4 v4.5.0 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/flatbuffers v2.0.0+incompatible // indirect
	github.com/google/go-querystring v1.1.0 // indirect
	github.com/gorilla/websocket v1.4.2 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.16.0 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/imkira/go-interpol v1.1.0 // indirect
	github.com/jinzhu/copier v0.3.5 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.15.15 // indirect
	github.com/lestrrat-go/strftime v1.0.3 // indirect
	github.com/magiconair/properties v1.8.6 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.19 // indirect
	github.com/mattn/go-sqlite3 v1.14.22 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/metakeule/fmtdate v1.1.2 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/go-wordwrap v1.0.1 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/mozillazg/go-httpheader v0.4.0 // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/opentracing/opentracing-go v1.2.1-0.20220228012449-10b1cf09e00b // indirect
	github.com/openzipkin/zipkin-go v0.4.1 // indirect
	github.com/panjf2000/ants/v2 v2.4.4 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pelletier/go-toml v1.9.5 // indirect
	github.com/pelletier/go-toml/v2 v2.1.0 // indirect
	github.com/pierrec/lz4/v4 v4.1.17 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.16.0 // indirect
	github.com/prometheus/client_model v0.3.0 // indirect
	github.com/prometheus/common v0.42.0 // indirect
	github.com/prometheus/procfs v0.10.1 // indirect
	github.com/remeh/sizedwaitgroup v1.0.0 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/saintfish/chardet v0.0.0-20230101081208-5e3ef4b5456d // indirect
	github.com/sanity-io/litter v1.5.5 // indirect
	github.com/segmentio/kafka-go v0.4.38 // indirect
	github.com/sergi/go-diff v1.0.0 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/afero v1.10.0 // indirect
	github.com/spf13/cast v1.5.0 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/spf13/viper v1.14.0 // indirect
	github.com/subosito/gotenv v1.4.1 // indirect
	github.com/tealeg/xlsx v1.0.5 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cdb v1.0.719 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm v1.0.719 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cynosdb v1.0.749 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/mongodb v1.0.719 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/postgres v1.0.745 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/redis v1.0.719 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sqlserver v1.0.719 // indirect
	github.com/texttheater/golang-levenshtein v1.0.1 // indirect
	github.com/tjfoc/gmsm v1.4.1 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.34.0 // indirect
	github.com/xeipuuv/gojsonpointer v0.0.0-20190905194746-02993c407bfb // indirect
	github.com/xeipuuv/gojsonreference v0.0.0-20180127040603-bd5ef7bd5415 // indirect
	github.com/xeipuuv/gojsonschema v1.2.0 // indirect
	github.com/xuri/efp v0.0.0-20220603152613-6918739fd470 // indirect
	github.com/xuri/nfp v0.0.0-20220409054826-5e722a1d9e22 // indirect
	github.com/yalp/jsonpath v0.0.0-20180802001716-5cc68e5049a0 // indirect
	github.com/yudai/gojsondiff v1.0.0 // indirect
	github.com/yudai/golcs v0.0.0-20170316035057-ecda9a501e82 // indirect
	go.mongodb.org/mongo-driver v1.12.1 // indirect
	go.opentelemetry.io/otel/exporters/jaeger v1.14.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.19.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.19.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp v1.19.0 // indirect
	go.opentelemetry.io/otel/exporters/stdout/stdouttrace v1.14.0 // indirect
	go.opentelemetry.io/otel/exporters/zipkin v1.14.0 // indirect
	go.opentelemetry.io/otel/metric v1.24.0 // indirect
	go.opentelemetry.io/otel/sdk v1.24.0 // indirect
	go.opentelemetry.io/otel/trace v1.24.0 // indirect
	go.opentelemetry.io/proto/otlp v1.0.0 // indirect
	go.uber.org/automaxprocs v1.5.3 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/ratelimit v0.3.0 // indirect
	golang.org/x/crypto v0.25.0 // indirect
	golang.org/x/net v0.27.0 // indirect
	golang.org/x/sys v0.22.0 // indirect
	golang.org/x/text v0.16.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	google.golang.org/genproto v0.0.0-20240401170217-c3f982113cda // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240415180920-8c6c420018be // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240415180920-8c6c420018be // indirect
	google.golang.org/grpc v1.63.2 // indirect
	google.golang.org/protobuf v1.33.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	moul.io/http2curl/v2 v2.3.0 // indirect
)

replace git.woa.com/msp/msp-cli =>  /Users/<USER>/Documents/code/GolandProjects/msp-utils/utils
