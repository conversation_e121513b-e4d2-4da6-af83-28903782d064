package register

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/mitchellh/mapstructure"
	"github.com/zeromicro/go-zero/core/logx"

	"git.woa.com/msp/msp-cli/pkg/urp/platform"
	"git.woa.com/msp/utils/commonutil"
	"git.woa.com/msp/utils/excel"
	"git.woa.com/msp/utils/excelutilv2"
	"git.woa.com/msp/utils/res"
	"git.woa.com/msp/utils/res/errcode"

	"git.woa.com/msp/urp-api/internal/config"
	"git.woa.com/msp/urp-api/internal/utils"
	"git.woa.com/msp/urp-api/pkg/consts"
	"git.woa.com/msp/urp-api/pkg/dao"
	"git.woa.com/msp/urp-api/pkg/display"
	"git.woa.com/msp/urp-api/pkg/display/aliyun"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/ack"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/ecs"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/eip"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/es"
	alikafka "git.woa.com/msp/urp-api/pkg/display/aliyun/kafka"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/lb/slb"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/mongodb"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/nas"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/nat"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/oss"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/polardb"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/rds"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/rds/mysql"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/rds/postgresql"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/rds/sqlserver"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/redis"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/rocketmq"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/sg"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/swas"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/vpc"
	"git.woa.com/msp/urp-api/pkg/display/aliyun/vpn"
	"git.woa.com/msp/urp-api/pkg/display/aws"
	awsConfig "git.woa.com/msp/urp-api/pkg/display/aws/config"
	awsDocumentdb "git.woa.com/msp/urp-api/pkg/display/aws/documentdb"
	awsDynamodb "git.woa.com/msp/urp-api/pkg/display/aws/dynamodb"
	"git.woa.com/msp/urp-api/pkg/display/aws/ec2"
	awsEfs "git.woa.com/msp/urp-api/pkg/display/aws/efs"
	awsEks "git.woa.com/msp/urp-api/pkg/display/aws/eks"
	awsElasticache "git.woa.com/msp/urp-api/pkg/display/aws/elasticache"
	awsElasticsearch "git.woa.com/msp/urp-api/pkg/display/aws/elasticsearch"
	awsElb "git.woa.com/msp/urp-api/pkg/display/aws/elb"
	awsLambda "git.woa.com/msp/urp-api/pkg/display/aws/lambda"
	awsAuroraMysql "git.woa.com/msp/urp-api/pkg/display/aws/rds/auroramysql"
	awsAuroraPostgresql "git.woa.com/msp/urp-api/pkg/display/aws/rds/aurorapostgresql"
	awsRds "git.woa.com/msp/urp-api/pkg/display/aws/rds/mysql"
	awsPostgresql "git.woa.com/msp/urp-api/pkg/display/aws/rds/postgresql"
	awsSqlServer "git.woa.com/msp/urp-api/pkg/display/aws/rds/sqlserver"
	awsS3 "git.woa.com/msp/urp-api/pkg/display/aws/s3"
	awsSns "git.woa.com/msp/urp-api/pkg/display/aws/sns"
	awsSqs "git.woa.com/msp/urp-api/pkg/display/aws/sqs"
	awsVpc "git.woa.com/msp/urp-api/pkg/display/aws/vpc"
	huaweiEcs "git.woa.com/msp/urp-api/pkg/display/huaweicloud/ecs"
	huaweiMongo "git.woa.com/msp/urp-api/pkg/display/huaweicloud/mongodb"
	huaweiObs "git.woa.com/msp/urp-api/pkg/display/huaweicloud/obs"
	huaweiRds "git.woa.com/msp/urp-api/pkg/display/huaweicloud/rds"
	huaweiRdsMysql "git.woa.com/msp/urp-api/pkg/display/huaweicloud/rds/mysql"
	huaweiRedis "git.woa.com/msp/urp-api/pkg/display/huaweicloud/redis"
	huaweiVpc "git.woa.com/msp/urp-api/pkg/display/huaweicloud/vpc"
	idcCFS "git.woa.com/msp/urp-api/pkg/display/idc/cfs"
	idcMysql "git.woa.com/msp/urp-api/pkg/display/idc/mysql"
	idcRedis "git.woa.com/msp/urp-api/pkg/display/idc/redis"
	"git.woa.com/msp/urp-api/pkg/display/idc/server"
	idcServer "git.woa.com/msp/urp-api/pkg/display/idc/server"
	pkgUtils "git.woa.com/msp/urp-api/pkg/utils"
)

const (
	// VSwitchsSheetName vpc 子网 sheet 页
	VSwitchsSheetName = "ACS_VPC_VSwitch"
	// VPCSheetName vpc sheet 页
	VPCSheetName = "ACS_VPC_VPC"
	// TemplateErrMsg 导入模板错误时提示语
	TemplateErrMsg = "文件内容错误，请勿修改原文件"

	AnalysisPoint = 2 //完成度加2%

	InfoSheetName = "info"

	// ResourceAliyunVpcSheet 阿里云资源清单 vpc Sheet
	ResourceAliyunVpcSheet = "VPC专有网络"
)

var (
	// SkipSheetSet 需要跳过处理的 sheet(已经单独处理过了或者是汇总类 sheet)
	SkipSheetSet = []string{
		VPCSheetName, VSwitchsSheetName,
		InfoSheetName,
	}

	// ErrPlatformError 平台错误
	ErrPlatformError = errors.New("PlatformError") //error var PlatformError should have name of the form ErrFoo (ST1012)
	// VpcNoUserIDError vpc sheet 不包含用户id
	VpcNoUserIDError = errors.New("VpcNoUserID")
	// NoVpcSheetError 缺少 vpc sheet
	NoVpcSheetError = errors.New("NoVpcSheet")

	// SupportProductMap 文件导入支持的列表
	SupportProductMap = map[string]string{

		// 阿里云
		ecs.SheetName:        ecs.Product,
		eip.SheetName:        eip.Product,
		redis.SheetName:      redis.Product,
		oss.SheetName:        oss.Product,
		vpc.VPCSheet:         vpc.Product,
		nas.NasSheet:         nas.Product,
		rds.SheetName:        rds.Product,
		slb.SheetName:        slb.ParentProductName,
		nat.SheetName:        nat.Product,
		polardb.SheetName:    polardb.Product,
		mongodb.MongoDBSheet: mongodb.Product,
		es.SheetName:         es.Product,
		alikafka.SheetName:   alikafka.Product,
		swas.SheetName:       swas.Product,
		ack.SheetName:        ack.Product,
		rocketmq.SheetName:   rocketmq.Product,
		sg.SheetName:         sg.Product,
		vpn.SheetName:        vpn.Product,

		// 阿里云资源导入
		vpc.ResourceAliyunVpcSheet:         vpc.Product,
		ecs.ResourceAliyunEcsSheet:         ecs.Product,
		rds.ResourceAliyunRdsSheet:         rds.Product,
		polardb.ResourceAliyunPolarDBSheet: polardb.Product,
		redis.ResourceAliyunRedisSheet:     redis.Product,
		mongodb.ResourceAliyunMongo:        mongodb.Product,
		oss.ResourceAliyunOssSheet:         oss.Product,
		slb.ResourceSlbSheet:               slb.ParentProductName,
		nas.ResourceAliyunNasSheet:         nas.Product,
		nat.ResourceAliyunNatSheet:         nat.Product,
		eip.ResourceAliyunEipSheet:         eip.Product,
		ack.ResourceAliyunAckSheet:         ack.Product,

		// IDC
		idcServer.SheetName: server.Product,
		idcMysql.SheetName:  idcMysql.Product,
		idcRedis.SheetName:  idcRedis.Product,
		idcCFS.SheetName:    idcCFS.Product,

		// 华为云
		huaweiEcs.SheetName:   huaweiEcs.Product,
		huaweiObs.SheetName:   huaweiObs.Product,
		huaweiRds.SheetName:   huaweiRds.Product,
		huaweiMongo.SheetName: huaweiMongo.Product,
		huaweiRedis.SheetName: huaweiRedis.Product,

		// aws
		ec2.SheetName: ec2.Product,
	}

	// SrcProductSheetNameMap 产品对应 sheet Map<src,Map<product,sheetName>>
	SrcProductSheetNameMap = map[string]map[string]string{
		// 阿里云
		string(platform.Aliyun): {
			ecs.Product:           "ACS_ECS_Instance",
			eip.Product:           "ACS_EIP_EipAddress",
			redis.Product:         "ACS_Redis_DBInstance",
			oss.Product:           "ACS_OSS_Bucket",
			vpc.Product:           "ACS_VPC_VPC",
			nas.Product:           "ACS_NAS_FileSystem",
			mysql.ParentProduct:   "ACS_RDS_DBInstance",
			mysql.Product:         "ACS_RDS_DBInstance",
			postgresql.Product:    "ACS_RDS_DBInstance",
			sqlserver.Product:     "ACS_RDS_DBInstance",
			slb.ParentProductName: "ACS_SLB_LoadBalancer",
			slb.Product:           "ACS_SLB_LoadBalancer",
			nat.Product:           "ACS_NAT_NatGateway",
			polardb.Product:       "ACS_PolarDB_DBCluster",
			mongodb.Product:       "ACS_MongoDB_DBInstance",
			es.Product:            "ACS_Elasticsearch_Service",
			alikafka.Product:      "ACS_KAFKA_Instance",
			swas.Product:          "ACS_SWAS_Instances",
			ack.Product:           "ACS_ACK_Cluster",
			rocketmq.Product:      "ACS_RocketMQ_Instance",
			sg.Product:            "安全组（SG）",
			vpn.Product:           vpn.SheetName,
		},
		// 华为云
		string(platform.HuaweiCloud): {
			huaweiEcs.Product:      huaweiEcs.SheetName,
			huaweiObs.Product:      huaweiObs.SheetName,
			huaweiRds.Product:      huaweiRds.SheetName,
			huaweiRdsMysql.Product: huaweiRds.SheetName,
			huaweiRedis.Product:    huaweiRedis.SheetName,
			huaweiMongo.Product:    huaweiMongo.SheetName,
			huaweiVpc.Product:      huaweiVpc.VpcSheetName,
		},
		// aws
		string(platform.Aws): {
			ec2.Product:                 ec2.SheetName,
			awsRds.Product:              awsRds.SheetName,
			awsPostgresql.Product:       awsPostgresql.SheetName,
			awsAuroraPostgresql.Product: awsAuroraPostgresql.SheetName,
			awsAuroraMysql.Product:      awsAuroraMysql.SheetName,
			awsSqlServer.Product:        awsSqlServer.SheetName,
			awsS3.Product:               awsS3.SheetName,
			awsVpc.Product:              awsVpc.SheetName,
			awsEfs.Product:              awsEfs.SheetName,
			awsConfig.Product:           awsConfig.SheetName,
			awsDocumentdb.Product:       awsDocumentdb.SheetName,
			awsDynamodb.Product:         awsDynamodb.SheetName,
			awsEks.Product:              awsEks.SheetName,
			awsElasticsearch.Product:    awsElasticsearch.SheetName,
			awsElasticache.Product:      awsElasticache.SheetName,
			awsElb.Product:              awsElb.SheetName,
			awsLambda.Product:           awsLambda.SheetName,
			awsSns.Product:              awsSns.SheetName,
			awsSqs.Product:              awsSqs.SheetName,
		},
		// IDC
		string(platform.Idc): {
			idcServer.Product: idcServer.SheetName,
			idcMysql.Product:  idcMysql.SheetName,
			idcRedis.Product:  idcRedis.SheetName,
			idcCFS.Product:    idcCFS.SheetName,
		},
	}
	// MigrateCenterSrcProductSheetNameMap 产品对应 sheet Map<src,Map<product,sheetName>>
	MigrateCenterSrcProductSheetNameMap = map[string]map[string]string{
		// 阿里云
		string(platform.Aliyun): {
			ecs.Product:         ecs.ResourceAliyunEcsSheet,
			eip.Product:         eip.ResourceAliyunEipSheet,
			redis.Product:       redis.ResourceAliyunRedisSheet,
			oss.Product:         oss.ResourceAliyunOssSheet,
			vpc.Product:         vpc.ResourceAliyunVpcSheet,
			nas.Product:         nas.ResourceAliyunNasSheet,
			mysql.ParentProduct: rds.ResourceAliyunRdsSheet,
			mysql.Product:       rds.ResourceAliyunRdsSheet,
			postgresql.Product:  rds.ResourceAliyunRdsSheet,
			sqlserver.Product:   rds.ResourceAliyunRdsSheet,

			slb.ParentProductName: slb.ResourceSlbSheet,
			slb.Product:           slb.ResourceSlbSheet,
			nat.Product:           nat.ResourceAliyunNatSheet,
			polardb.Product:       polardb.ResourceAliyunPolarDBSheet,
			mongodb.Product:       mongodb.ResourceAliyunMongo,
			ack.Product:           ack.ResourceAliyunAckSheet,
		},
	}
)

// ProcessImportV2 新版离线导入处理主流程
// nolint
func ProcessImportV2(ctx context.Context, c config.Config, file string, job *dao.Job, l logx.Logger,
	headers *[]*dao.JobProductTableHeader, urpDataList *[]*dao.Data, urpDraftLineList *[]*display.UrpDraftLine,
) ([]string, []string, map[string][]string, map[string][]string, []display.FiledHeadType, error) {
	//defer func() {
	//	// 删除本地文件
	//	_ = os.RemoveAll(file)
	//}()
	p := excelutilv2.NewParser(file)
	// 默认只有一条表头
	p.DataIndexOffset = 1
	p.AllowFieldRepeat = true
	data, err := p.Parse()
	if err != nil {
		l.Error("parse excel failed", logx.Field("AkHash", job.AkHash), logx.Field("err", err))
		return nil, nil, nil, nil, nil, err
	}

	// 阿里云账单导入
	if job.Src == string(platform.Aliyun) && job.JobType == consts.ExpenseImport {
		return processExpenseImport(ctx, job, data, urpDataList, urpDraftLineList, headers)
	}

	// 手工导入源资源
	if job.Src == string(platform.Idc) && job.JobType == consts.Import {
		// IDC手工导入资源模板,从第三行开始解析
		for _, sheetData := range data.SheetNameData {
			if sheetData.RowTotal < 3 {
				return nil, nil, nil, nil, nil, res.NewCmgErr(errcode.TemplateErr,
					errcode.DefaultErrMsgMap[errcode.TemplateErr])
			}
		}
		p.DataIndexOffset = 3
		p.FieldHeadRowIndex = 3
		data, err = p.Parse()
		if err != nil {
			l.Error("parse excel failed", logx.Field("AkHash", job.AkHash), logx.Field("err", err))
			return nil, nil, nil, nil, nil, err
		}
	}

	sheetList := getSheetList(data)
	if len(sheetList) == 0 {
		return nil, nil, nil, nil, nil, res.NewCmgErr(errcode.TemplateErr,
			errcode.DefaultErrMsgMap[errcode.TemplateErr])
	}
	var productList []string
	var productInfos []string
	mustSheetHead := make(map[string][]string)
	tipSheetHead := make(map[string][]string)
	tipTypes := make([]display.FiledHeadType, 0)
	pm := platformProductDisplayMetasMap[platform.Platform(job.Src)]
	for _, sheet := range sheetList {
		sheetData := data.SheetNameData[sheet]
		metas := pm[SupportProductMap[sheet]]
		productList = append(productList, SupportProductMap[sheet])

		if len(metas) == 1 {
			meta := metas[0]
			isRequiredHead, heads, _tipTypes := getMissingHead(meta.FieldHead, sheetData.FieldKeys)
			if isRequiredHead {
				mustSheetHead[sheet] = heads
				continue
			}
			if len(heads) > 0 {
				tipSheetHead[sheet] = heads
				tipTypes = append(tipTypes, _tipTypes...)
			}

			productInfos = append(productInfos, meta.ProductName)
			*headers = append(*headers, &dao.JobProductTableHeader{
				Product:               meta.Product,
				ParentProduct:         meta.ParentProduct,
				ParentProductShowName: meta.ParentProductName,
				// JobID:                 job.JobID,
				ShowName:    meta.ProductName,
				BasicHeader: meta.BasicHeader,
			})
			logger := logx.WithContext(ctx).WithFields(logx.Field("product", meta.Product),
				logx.Field("sheetName", sheetData.SheetName))
			urpData, draftLine := meta.TransformRowDataV2(c, sheetData, logger, job)
			// hardcode 方式兼容 vswitchs 关联 vpc
			if _, ok := data.SheetNameData[VSwitchsSheetName]; ok && sheet == "ACS_VPC_VPC" {
				isRequiredHead, heads, _tipTypes := getMissingHead(vpc.VSwitchFieldHead, data.SheetNameData[VSwitchsSheetName].FieldKeys)
				if isRequiredHead {
					mustSheetHead[VSwitchsSheetName] = heads
					continue
				}
				if len(heads) > 0 {
					tipSheetHead[VSwitchsSheetName] = heads
					tipTypes = append(tipTypes, _tipTypes...)
				}

				err := vpc.ProcessVSwitch(ctx, data.SheetNameData[VSwitchsSheetName], &urpData)
				if err != nil {
					l.Error("ProcessVSwitch failed", logx.Field("err", err))
					return nil, nil, nil, nil, nil, err
				}
			}
			if len(urpData) != 0 || len(draftLine) != 0 {
				productInfos = append(productInfos, meta.Product)
			}
			*urpDataList = append(*urpDataList, urpData...)
			*urpDraftLineList = append(*urpDraftLineList, draftLine...)
		} else {
			for _, meta := range metas {
				*headers = append(*headers, &dao.JobProductTableHeader{
					Product:               meta.Product,
					ParentProduct:         meta.ParentProduct,
					ParentProductShowName: meta.ParentProductName,
					// JobID:                 job.JobID,
					ShowName:    meta.ProductName,
					BasicHeader: meta.BasicHeader,
				})
				// 校验表头是否满足字段
				isRequiredHead, heads, _tipTypes := getMissingHead(meta.FieldHead, sheetData.FieldKeys)
				if isRequiredHead {
					mustSheetHead[sheet] = heads
					continue
				}
				if len(heads) > 0 {
					tipSheetHead[sheet] = heads
					tipTypes = append(tipTypes, _tipTypes...)
				}

				logger := logx.WithContext(ctx).WithFields(logx.Field("product", meta.Product),
					logx.Field("sheetName", sheetData.SheetName))
				urpData, draftLine := meta.TransformRowDataV2(c, sheetData, logger, job)
				if len(urpData) != 0 || len(draftLine) != 0 {
					productInfos = append(productInfos, meta.Product)
				}
				*urpDataList = append(*urpDataList, urpData...)
				*urpDraftLineList = append(*urpDraftLineList, draftLine...)
			}
		}
	}
	return productList, productInfos, mustSheetHead, tipSheetHead, tipTypes, nil
}

// getSheetList 获取sheet列表
func getSheetList(f *excelutilv2.ExcelData) []string {
	sheetR := make([]string, 0)
	for sheetName := range f.SheetNameData {
		_, ok := SupportProductMap[sheetName]
		if ok {
			sheetR = append(sheetR, sheetName)
		}
	}
	return sheetR
}

// processExpenseImport 阿里云账单导入
func processExpenseImport(ctx context.Context, job *dao.Job, f *excelutilv2.ExcelData, urpDataList *[]*dao.Data,
	urpDraftLineList *[]*display.UrpDraftLine, headers *[]*dao.JobProductTableHeader) ([]string, []string,
	map[string][]string, map[string][]string, []display.FiledHeadType, error,
) {
	var sheetData *excelutilv2.SheetData
	for _, data := range f.SheetIndexData {
		sheetData = data
		break
	}

	isNewFile := aliyun.DetectVersion(sheetData)
	if isNewFile {
		aliyun.NewToOldConvert(sheetData)
	}

	var productList []string
	var productInfos []string
	// mustSheetHead 售前必要字段
	mustSheetHead := make(map[string][]string)
	// tipSheetHead 售前可选字段,售中必要字段
	tipSheetHead := make(map[string][]string)
	tipTypes := make([]display.FiledHeadType, 0)

	pm := platformProductDisplayMetasMap[platform.Platform(job.Src)]
	for _, metas := range pm {
		for _, meta := range metas {
			if !meta.IsSupportExpense {
				continue
			}
			if meta.ParentProduct != "" {
				productList = append(productList, meta.ParentProduct)
			} else {
				productList = append(productList, meta.Product)
			}
			*headers = append(*headers, &dao.JobProductTableHeader{
				Product:               meta.Product,
				ParentProduct:         meta.ParentProduct,
				ParentProductShowName: meta.ParentProductName,
				JobID:                 job.JobID,
				ShowName:              meta.ProductName,
				BasicHeader:           meta.BasicHeader,
			})
			isRequiredHead, heads, _tipTypes := getMissingHead(meta.ExpenseFieldHead, sheetData.FieldKeys)
			if isRequiredHead {
				mustSheetHead["明细账单"] = MergeSlice(mustSheetHead["明细账单"], heads)
				continue
			}
			if len(heads) > 0 {
				tipSheetHead["明细账单"] = MergeSlice(tipSheetHead["明细账单"], heads)
				tipTypes = append(tipTypes, _tipTypes...)
			}
			urpData, draftLine := meta.ExpenseDetail(ctx, sheetData, job)
			if len(urpData) > 0 || len(draftLine) > 0 {
				productInfos = append(productInfos, meta.Product)
			}
			*urpDataList = append(*urpDataList, urpData...)
			*urpDraftLineList = append(*urpDraftLineList, draftLine...)
		}
	}

	if isNewFile {
		mustSheetHead = aliyun.OldToNewConvert(mustSheetHead)
		tipSheetHead = aliyun.OldToNewConvert(tipSheetHead)
	}

	return productList, productInfos, mustSheetHead, tipSheetHead, tipTypes, nil
}

func ParseXlsx(ctx context.Context, file string, job *dao.Job, logger logx.Logger, config config.Config, isFixData bool) (dataList []*dao.Data, dataStream []chan *dao.Data, cmgErr *res.CmgErr) {
	defer func() {
		if r := recover(); r != nil {
			buf := make([]byte, 1024)
			n := runtime.Stack(buf, false)
			fmt.Printf("%s", buf[:n])
			cmgErr = res.NewCmgErrMsg("TransXlsxDataToUrpData panic")
		}
	}()
	dataStream = make([]chan *dao.Data, 1)
	excelSrc := GetExcelKey(job.Src, string(job.JobType))
	var xlsxParsedData *excel.ParsedData
	var sheetMissingHeaderMap map[string][]string
	var err error
	switch job.JobType {
	case consts.MigrateCenter:
		xlsxParsedData, sheetMissingHeaderMap, err = excel.ReadXlsxByFileMapping(excelSrc, file, aliyun.GetAliyunMap())
	case consts.Import, consts.ToolScanImport:
		xlsxParsedData, sheetMissingHeaderMap, err = excel.ReadXlsxByTag(excelSrc, file)
	case consts.ExpenseImport:
		parser, err2 := excel.ReadCsvByTag(excelSrc, file)
		if err2 != nil {
			return nil, nil, res.NewCmgErrMsg("ReadCsvByTag err")
		}
		sheetMissingHeaderMap = parser.GetMissingHeaders()

		// 遍历可用的sheet（CSV通常只有一个）
		for _, sheetName := range parser.GetAvailableSheets() {
			err = parser.SetCurrentSheet(sheetName)
			if err != nil {
				continue
			}

			// 逐行处理数据
			for {
				data, errRow, hasMore, err := parser.NextRow()
				if err != nil {
					// 处理错误
					break
				}
				if !hasMore {
					// 没有更多行
					break
				}

				if errRow != nil {
					// 处理错误行
					fmt.Printf("Error row: %+v\n", errRow)
				} else {
					// 处理正确行
					fmt.Printf("Data: %+v\n", data)
				}
			}
		}
	}
	//if job.JobType == consts.MigrateCenter {
	//	xlsxParsedData, sheetMissingHeaderMap, err = excel.ReadXlsxByFileMapping(excelSrc, file, aliyun.GetAliyunMap())
	//} else if job.JobType == consts.Import || job.JobType == consts.ToolScanImport || job.JobType == consts.ExpenseImport {
	//	xlsxParsedData, sheetMissingHeaderMap, err = excel.ReadXlsxByTag(excelSrc, file)
	//}
	if err != nil {
		return nil, nil, res.NewCmgErrMsg("ReadXlsxError")
	}

	if ctx.Err() != nil {
		logger.Info("context canceled by user in ParseXlsx part")
		return nil, nil, res.NewCmgErrMsg(context.Canceled.Error())
	}
	if len(sheetMissingHeaderMap) != 0 {
		var errs []string
		for sheet, heads := range sheetMissingHeaderMap {
			headerStr := "[" + strings.Join(heads, ", ") + "]"
			errs = append(errs, fmt.Sprintf("sheet:%s, missing column:%s ", sheet, headerStr))
		}
		return nil, nil, res.NewCmgErrMsg(utils.TruncateString(strings.Join(errs, ";\n"), 1024))
	}
	// 若存在根据 表头近义词组或表头内容匹配情况，输出日志和微信告警通知
	if len(xlsxParsedData.SheetHeaderChangeMap) > 0 {
		logger.WithFields(
			logx.Field("src", excelSrc),
			logx.Field("headers", xlsxParsedData.SheetHeaderChangeMap),
		).Infof("sheet header changed")
		// todo 发送通知告警
		//wechat_sdk.SendWechatMsg(
		//	fmt.Sprintf("UIN[%s],SubUin[%s] 通过 [%s] 导入调研数据在阶段【文件预处理】表头匹配变更, jobID[%s] ,源端[%s], 名称 [%s] ,部门[%s],中文名 [%s],表头信息[%v]",
		//		job.Uin, job.SubAccountUin, job.JobType, job.JobID, job.Src, logc.GetRtxName(ctx),
		//		logc.GetDeptName(ctx), logc.GetChineseName(ctx), xlsxParsedData.SheetHeaderChangeMap), false)
	}
	if len(xlsxParsedData.SheetMap) == 0 {
		return nil, nil, res.NewCmgErrMsg("missing sheets")
	}
	//if !isFixData && job.Src != string(platform.Idc) && job.Src != string(platform.Aws) {
	//	// 校验 Excel 内容是否合法
	//	validateStrategy := SheetStrategyMap[job.JobType]
	//	err = validateStrategy.CheckSheet(xlsxParsedData, job, config, logger)
	//	if err != nil {
	//		return nil, err
	//	}
	//}

	dataList, r := parseToUrpData(ctx, job, xlsxParsedData, logger, config)
	return dataList, r
}

func parseToUrpData(ctx context.Context, job *dao.Job, xlsxParsedData *excel.ParsedData, logger logx.Logger, config config.Config) ([]*dao.Data, *res.CmgErr) {

	dataList := make([]*dao.Data, 0)
	// 离线导出 VPC 和 子网是两个 sheet， 需要单独处理
	if job.JobType == consts.Import && job.Src != string(platform.Idc) {
		// 处理vpc和子网
		vpcSheet, hasVpc := xlsxParsedData.SheetMap[VPCSheetName]
		subnetSheet, hasSubnet := xlsxParsedData.SheetMap[VSwitchsSheetName]
		if hasVpc && hasSubnet {
			vpcAndSubnetDataList, err := vpc.ProcessVpcAndSubnet(vpcSheet, subnetSheet, job, logger)
			if err != nil {
				return nil, res.NewCmgErrMsg(err.Error())
			}
			dataList = append(dataList, vpcAndSubnetDataList...)
		}
	}
	var err error
	pm := platformProductDisplayMetasMap[platform.Platform(job.Src)]
	// 处理xlsxData
	for sheetName, parsedSheetData := range xlsxParsedData.SheetMap {
		if ctx.Err() != nil {
			logger.Info("context canceled by user in ParseXlsx part")
			return nil, res.NewCmgErrMsg(context.Canceled.Error())
		}

		if commonutil.InSlice(sheetName, SkipSheetSet) {
			continue
		}

		metas := pm[SupportProductMap[sheetName]]
		processErrFlag := false
		for _, meta := range metas {
			correctDataList := make([]*dao.Data, 0)
			instanceID := ""
			logger.Infow("start to process sheet", logx.Field("sheet", sheetName))
			switch job.JobType {
			case consts.Import, consts.ToolScanImport:
				if meta.TransXlsxDataToUrpData == nil {
					logger.Errorw("meta.TransXlsxDataToUrpData is nil", logx.Field("meta", meta.Product))
					continue
				}
				correctDataList, instanceID, err = meta.TransXlsxDataToUrpData(job, parsedSheetData.RowList, logger, config)
				if err != nil {
					return nil, res.NewCmgErrMsg(err.Error())
				}
			case consts.MigrateCenter:
				if meta.TransResourceToUrpData == nil {
					logger.Errorw("meta.TransResourceToUrpData is nil", logx.Field("meta", meta.Product))
					continue
				}
				correctDataList, instanceID, err = meta.TransResourceToUrpData(job, parsedSheetData.RowList, logger, config)
				if err != nil {
					return nil, res.NewCmgErrMsg(err.Error())
				}
			}

			dataList = append(dataList, correctDataList...)
			// 处理错误数据
			if !processErrFlag && len(parsedSheetData.RawRowList) > 0 {
				processErrFlag = true
				for i, row := range parsedSheetData.RawRowList {
					if allNil(row.RawData) {
						logger.Infof("ignore the emtpy row (see sheet:%s)", sheetName)
						continue
					}
					var idStr string
					sheets := []string{idcMysql.SheetName, idcRedis.SheetName, idcServer.SheetName}
					if job.Src == string(platform.Idc) && job.JobType == consts.ToolScanImport && commonutil.InSlice(sheetName, sheets) { //idc 文件导入 生成 id
						instanceName := row.RawData["InstanceName"].(string)
						//处理唯一标识 错误数据的instanceName为空时，自动生成
						if instanceName == "" {
							switch sheetName {
							case idcMysql.SheetName:
								idStr = fmt.Sprint("mysql-", strconv.FormatInt(time.Now().Unix(), 10), "-", i)
							case idcRedis.SheetName:
								idStr = fmt.Sprint("redis-", strconv.FormatInt(time.Now().Unix(), 10), "-", i)
							case idcServer.SheetName:
								ips := row.RawData["PrivateIPAddress"].(string)
								ipArr := strings.Split(ips, ",")
								if len(ips) == 0 {
									return nil, res.NewCmgErrMsg("sheet %s:instanceID(%s) ip not found", sheetName, instanceID)
								}
								idStr = ipArr[0]
							}
						} else {
							idStr = instanceName
						}
					} else {
						id, ok := row.RawData[instanceID]
						if !ok {
							return nil, res.NewCmgErrMsg("sheet %s :instanceID(%s)not found", sheetName, instanceID)
						}
						idStr, ok = id.(string)
						if !ok {
							return nil, res.NewCmgErrMsg("sheet:%s:instanceID(%s) not string value is %v", instanceID, id)
						}
						if idStr == "" {
							return nil, res.NewCmgErrMsg("sheet %s :instanceID(%s)not found", sheetName, instanceID)
						}
					}

					urpData := &dao.Data{
						BaseModel: dao.BaseModel{
							Deleted:   false,
							DeletedAt: 0,
						},
						AppID:         job.AppID,
						SubAccountUin: job.SubAccountUin,
						Uin:           job.Uin,
						ScanAt:        job.AnalysisAt,
						Src:           job.Src,
						JobID:         job.JobID,
						Product:       SupportProductMap[sheetName],
						SourceData:    row,
						InstanceID:    idStr,
						Draft:         true,
					}
					dataList = append(dataList, urpData)
				}
			}
		}
		//aws 账单数据梳理
		if job.JobType == consts.ExpenseImport {
			if job.Src == string(platform.Aws) { //aws账单异常数据处理
				reviseCanOmitIDData(&parsedSheetData, logger)
				//过滤异常数据 ，无效数据，rowdata
				parsedSheetData.RawRowList = filterAwsBillErrRow(parsedSheetData.RawRowList)
				if len(parsedSheetData.RawRowList) > 0 {
					for _, row := range parsedSheetData.RawRowList {
						if allNil(row.RawData) {
							continue
						}
						var idStr string
						productCode, ok := row.RawData["ProductServicecode"]
						id, ok := row.RawData["LineItemResourceId"]
						idStr, ok2 := id.(string)
						if !ok || !ok2 || idStr == "" {
							continue
						}
						urpData := &dao.Data{
							BaseModel: dao.BaseModel{
								Deleted:   false,
								DeletedAt: 0,
							},
							AppID:         job.AppID,
							SubAccountUin: job.SubAccountUin,
							Uin:           job.Uin,
							ScanAt:        job.AnalysisAt,
							Src:           job.Src,
							JobID:         job.JobID,
							SourceData:    row,
							InstanceID:    idStr,
							Draft:         true,
						}
						switch productCode {
						case ec2.BillProductServiceCode:
							urpData.Product = ec2.Product
						case awsRds.BillProductServiceCode:
							urpData.Product = awsRds.Product
							split := strings.Split(urpData.InstanceID, ":")
							urpData.InstanceID = split[len(split)-1]
						case awsS3.BillProductServiceCode:
							urpData.Product = awsS3.Product
						}

						dataList = append(dataList, urpData)
					}
				}
				//处理有效数据
				if parsedSheetData.RowList == nil || len(parsedSheetData.RowList) == 0 {
					continue
				}
			}
			if job.Src == string(platform.Aliyun) {
				//过滤异常数据 ，无效数据，rowdata
				parsedSheetData.RawRowList = filterAliyunBillErrRow(parsedSheetData.RawRowList)
				if len(parsedSheetData.RawRowList) > 0 {
					idMap := make(map[string]string)
					for _, row := range parsedSheetData.RawRowList {
						if allNil(row.RawData) {
							continue
						}
						rowData := &aliyun.AliyunBillRow{}
						err := mapstructure.Decode(row.RawData, rowData)
						if err != nil {
							continue
						}
						if _, ok := idMap[rowData.InstanceID]; !ok { //根据 id 去重
							idMap[rowData.InstanceID] = rowData.InstanceID
						} else {
							continue
						}
						urpData := &dao.Data{
							BaseModel: dao.BaseModel{
								Deleted:   false,
								DeletedAt: 0,
							},
							AppID:         job.AppID,
							SubAccountUin: job.SubAccountUin,
							Uin:           job.Uin,
							ScanAt:        job.AnalysisAt,
							Src:           job.Src,
							JobID:         job.JobID,
							SourceData:    row,
							InstanceID:    rowData.InstanceID,
							Draft:         true,
						}
						switch rowData.ProductCode {
						case ecs.Product, mongodb.Product:
							urpData.Product = rowData.ProductCode
						case alikafka.BillProductCode:
							urpData.Product = alikafka.Product
						case rocketmq.BillProductCode:
							urpData.Product = rocketmq.Product
						case rds.BillProductCode:
							urpData.ParentProduct = rds.Product
							productMap := pkgUtils.GetProductMap(rowData.InstanceConfig)
							if productName, ok := productMap["数据库类型"]; ok {
								switch productName {
								case mysql.BillProductCode:
									urpData.Product = mysql.Product
								case sqlserver.BillProductCode:
									urpData.Product = sqlserver.Product
								case postgresql.BillProductCode:
									urpData.Product = postgresql.Product
								}
							}
						}

						dataList = append(dataList, urpData)
					}
				}
				//处理有效数据
				if parsedSheetData.RowList == nil || len(parsedSheetData.RowList) == 0 {
					continue
				}

			}

			for _, meta := range pm {
				for _, displayMeta := range meta {
					correctDataList := make([]*dao.Data, 0)
					//instanceID := ""
					if !displayMeta.IsSupportExpense || displayMeta.ExpenseDetailV2 == nil {
						continue
					}
					logger.Infow("开始处理账单导入", logx.Field("meta", displayMeta.Product))
					correctDataList, err = displayMeta.ExpenseDetailV2(ctx, parsedSheetData.RowList, job, logger, config)
					if err != nil {
						return nil, res.NewCmgErrMsg(err.Error())
					}
					if len(correctDataList) == 0 {
						continue
					}
					dataList = append(dataList, correctDataList...)
				}

			}
		}

	}
	return dataList, nil
}

func allNil(m map[string]any) bool {
	for _, v := range m {
		if v != nil {
			return false
		}
	}
	return true
}

// reviseCanOmitIDData 修正可以忽略id的数据，如aws config 产品
func reviseCanOmitIDData(parsedSheetData *excel.ParsedSheetData, logger logx.Logger) {
	if parsedSheetData.RawRowList != nil {
		var rawRowList = make([]excel.RawRow, 0)
		for index, row := range parsedSheetData.RawRowList {
			val, ok := row.ErrData["LineItemResourceId"]
			productServiceCode, sok := row.RawData["ProductServicecode"]
			if !sok || !ok {
				continue
			}

			// 如果只有id一个缺少必填字段的错误,并且该产品允许缺少id，则修补填id修正该错误,
			var revised = false
			if productServiceCode != nil {
				productCanEmptyId := commonutil.InSlice(productServiceCode.(string), AwsBillSupportEmptyIDProducts)
				if val == "缺少必填字段" && len(row.ErrData) == 1 && productCanEmptyId {
					tags, okk := row.RawData["ResourceTags"]
					if okk {
						var tagMap map[string]string
						e := json.Unmarshal([]byte(tags.(string)), &tagMap)
						if e != nil {
							logger.Infow("parsedSheetData.RawRowList.RawData tag 反序列化失败", logx.Field("tag", tags))
							continue
						}
						row.RawData["ResourceTags"] = tagMap
					}
					//补充id
					row.RawData["LineItemResourceId"] = fmt.Sprintf("%s-%s-%d", productServiceCode,
						time.Now().Format("20060102150405.000"), index)
					var data aws.AwsBillRow
					e := mapstructure.Decode(row.RawData, &data)
					if e != nil {
						logger.Infow("parsedSheetData.RawRowList.RawData 类型转换失败", logx.Field("data", row.RawData))
						continue
					}
					revised = true
					parsedSheetData.RowList = append(parsedSheetData.RowList, data)
				}
			}
			if !revised {
				rawRowList = append(rawRowList, row)
			}
		}
		parsedSheetData.RawRowList = rawRowList
	}
}

func filterAwsBillErrRow(errRows []excel.RawRow) []excel.RawRow {
	filteredErrRows := make([]excel.RawRow, 0)
	ids := make([]string, len(errRows))
	for _, errRow := range errRows {
		if id, ok := errRow.ErrData["LineItemResourceId"]; !ok { // 必须LineItemResourceId不为空
			if productServiceCode, ok := errRow.ErrData["ProductServicecode"]; ok {
				if commonutil.InSlice(productServiceCode, AwsBillSupportProduct) { //过滤支持的产品的账单数据
					//去重 resourceID
					if !commonutil.InSlice(id, ids) {
						ids = append(ids, id)
						filteredErrRows = append(filteredErrRows, errRow)
					}
				}
			}
		}
	}
	return filteredErrRows
}

func filterAliyunBillErrRow(errRows []excel.RawRow) []excel.RawRow {
	filteredErrRows := make([]excel.RawRow, 0)
	idMap := make(map[string]time.Time)
	idRowMap := make(map[string]excel.RawRow)
	for _, errRow := range errRows {
		if id, ok := errRow.ErrData["资产/资源实例ID"]; !ok { // 必须ResourceId不为空
			rowData := &aliyun.AliyunBillRow{}
			err := mapstructure.Decode(errRow.ErrData, rowData)
			if err != nil || rowData.InstanceID == "" || rowData.ProductCode == "" || rowData.ConsumeTime == "" {
				continue
			}
			if !commonutil.InSlice(rowData.ProductCode, AliyunBillSupportProduct) {
				continue
			}
			// 解析消费时间
			consumeTime, err := utils.FormatTimeToDateStr(rowData.ConsumeTime)
			if err != nil {
				logx.Info("[filterAliyunBillErrRow]消费时间格式错误：", rowData.ConsumeTime)
				continue
			}
			//去重 resourceID
			timeStr, ok := idMap[id]
			if !ok {
				idMap[id] = consumeTime
				idRowMap[id] = errRow
			} else {
				if timeStr.Before(consumeTime) {
					idMap[id] = consumeTime
					idRowMap[id] = errRow
				}
			}
		}
	}
	for _, v := range idRowMap {
		filteredErrRows = append(filteredErrRows, v)
	}
	return filteredErrRows
}
