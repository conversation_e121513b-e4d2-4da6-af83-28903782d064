// Package file logic pkg
package file

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"

	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/shakinm/xlsReader/xls"
	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	logc "git.woa.com/msp/common-sdk/go-zero/log"
	wechat_sdk "git.woa.com/msp/common-sdk/wechat-sdk"
	"git.woa.com/msp/msp-cli/pkg/urp/platform"
	handlerConsts "git.woa.com/msp/urp-api/internal/consts"
	idcServer "git.woa.com/msp/urp-api/pkg/display/idc/server"
	"git.woa.com/msp/utils/excel"
	"git.woa.com/msp/utils/idencoder"
	"git.woa.com/msp/utils/res"
	"git.woa.com/msp/utils/res/errcode"

	"git.woa.com/msp/urp-api/internal/svc"
	"git.woa.com/msp/urp-api/internal/types"
	"git.woa.com/msp/urp-api/internal/utils"
	"git.woa.com/msp/urp-api/pkg/consts"
	"git.woa.com/msp/urp-api/pkg/dao"
	"git.woa.com/msp/urp-api/pkg/display/register"
)

const (
	KB = 1024
	MB = KB * 1024
	GB = MB * 1024

	// randByteLength 文件名随机结尾， 16进制 2bit
	randByteLength = 2
	// ExtXLS xlsx 后缀名
	ExtXLS = "xlsx"

	maxFileSize = 20 * MB // 20MB 在mac环境下测试发现这个文件是按1000递进，还是以windows为准先 使用配置中心的 maxBytes

)

var (
	mu      sync.Mutex
	StopChs = make(map[string]chan bool)
	// XLSFileHeader xls 文件头
	XLSFileHeader = []byte{0xD0, 0xCF, 0x11, 0xE0}
	// XLSXFileHeader xlsx 文件头
	XLSXFileHeader = []byte{0x50, 0x4B, 0x03, 0x04}
)

// ImportWithAnalysisLogic logic
type ImportWithAnalysisLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// NewImportWithAnalysisLogic new logic
func NewImportWithAnalysisLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ImportWithAnalysisLogic {
	return &ImportWithAnalysisLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// ImportWithAnalysis function do
func (l *ImportWithAnalysisLogic) ImportWithAnalysis(
	req *types.ImportWithAnalysisReq, r *http.Request,
) (resp *types.ImportWithAnalysisResp, err error) {

	jobID := idencoder.GenerateUniqueID("urp-", 8)
	l.Logger = l.Logger.WithFields(logx.Field("AppID", req.AppID),
		logx.Field("Uin", req.Uin),
		logx.Field("SubAccountUin", req.SubAccountUin),
		logx.Field("ReqID", req.RequestID),
		logx.Field("jobID", jobID))

	// 任务创建
	job := &dao.Job{
		AppID:         req.AppID,
		Uin:           req.Uin,
		SubAccountUin: req.SubAccountUin,
		JobID:         jobID,
		Src:           req.Src,
		Status:        consts.JobWating, // 初始状态
		JobType:       consts.JobType(req.JobType),
		Progress:      0,
		AnalysisAt:    uint64(req.ScanAt),
	}
	if 0 == req.ScanAt {
		job.AnalysisAt = uint64(time.Now().Unix())
	}
	// 只有在没有错误时才继续执行
	err = l.svcCtx.Dao.Job.Add(job)
	if err != nil {
		return nil, err
	}

	// 切换上下文启用协程
	ctx, cancel := context.WithCancel(context.Background())
	log := logx.WithContext(ctx).WithFields(logx.Field("jobID", job.JobID))
	cancelChan := make(chan bool)
	mu.Lock()
	StopChs[job.JobID] = cancelChan
	mu.Unlock()

	// 监听取消信号
	go func(ctx context.Context, log logx.Logger, svcCtx *svc.ServiceContext) {
		defer cancel()
		go openFile(ctx, log, svcCtx, req.Uin, r, job, false)
		select {
		case <-ctx.Done():
			log.Infof("job finish %s", job.JobID)
			delete(StopChs, job.JobID)
		case <-StopChs[job.JobID]:
			log.Infof("cancel job %s", job.JobID)
			delete(StopChs, job.JobID)
		}

	}(ctx, log, l.svcCtx)

	resp = &types.ImportWithAnalysisResp{
		JobID: jobID,
	}
	return resp, nil
}

// UploadProgress 上传进度
type UploadProgress struct {
	TotalBytes    int64
	UploadedBytes int64
	Job           *dao.Job
	// Dao           *dao.Dao
	Ctx context.Context
}

// ProgressReader
type ProgressReader struct {
	reader   io.Reader
	progress *UploadProgress
}

func (up *UploadProgress) UpdateProgress() error {
	if up.Ctx.Err() != nil {
		return context.Canceled
	}
	progress := float32(up.UploadedBytes) / float32(up.TotalBytes)
	// up.Job.NormalMsg = fmt.Sprintf("文件信息已上传: %.2f%%\n", progress*100) //mark 国际化请注意
	up.Job.Status = consts.JobAnalysis
	up.Job.Progress = progress * 0.9 * 100 //上传占全进度90%这里打九折
	return nil
}

func (pr *ProgressReader) Read(p []byte) (int, error) {
	n, err := pr.reader.Read(p)
	if err != nil {
		return n, err
	}
	pr.progress.UploadedBytes += int64(n)
	err = pr.progress.UpdateProgress()
	return n, err
}

func openFile(ctx context.Context, l logx.Logger, svcCtx *svc.ServiceContext, uin string, r *http.Request, job *dao.Job, isFixData bool) {
	fileMap := r.MultipartForm.File
	for _, fhs := range fileMap {
		for _, fh := range fhs {
			l.Infof("file size(byte):%d, size(MB):%s", fh.Size, formatFileSize(fh.Size))
			if fh.Size > svcCtx.Config.MaxBytes {
				job.ErrMsg = "The file size exceeds the allowed value" //国际化注意
				dataMap := make(map[string]interface{})
				dataMap["status"] = consts.JobStop
				str, e := res.PersistenceErr(res.NewCmgErr(errcode.InvalidParameter, job.ErrMsg), job.ErrMsg)
				if e != nil {
					l.Errorw("Marshal error", logx.Field("err", e))
				}
				dataMap["err_msg"] = str
				svcCtx.Dao.Job.Update(job.ID, dataMap)
				return
			}
			f, err := fh.Open()
			if err != nil {
				l.Errorw("Failed to open upload file stream", logx.Field("err", err))
				job.ErrMsg = "Open file stream exception" //国际化注意
				dataMap := make(map[string]interface{})
				dataMap["status"] = consts.JobStop
				str, e := res.PersistenceErr(res.NewCmgErr(errcode.BizError, job.ErrMsg), job.ErrMsg)
				if e != nil {
					l.Errorw("Marshal error", logx.Field("err", e))
				}
				dataMap["err_msg"] = str
				svcCtx.Dao.Job.Update(job.ID, dataMap)
				return
			}

			fullFilePath, err := readFile(ctx, l, svcCtx, uin, f, fh, job)
			if err != nil {
				l.Errorw("Failed to open upload file stream", logx.Field("err", err))
				return
			}
			if ctx.Err() != nil {
				return
			}

			JobAnalysis(ctx, l, svcCtx, fullFilePath, fh.Filename, job, isFixData)
		}
	}
}

func readFile(ctx context.Context, l logx.Logger, svcCtx *svc.ServiceContext, uin string, file multipart.File, fileHeader *multipart.FileHeader, job *dao.Job) (filePath string, err error) {
	var readerErr error
	defer func() {
		closeErr := file.Close()
		dataMap := make(map[string]interface{})
		if closeErr != nil {
			l.Errorw("close file error", logx.Field("err", closeErr))
		}
		if readerErr != nil {
			job.ErrMsg = "File error, please check and upload again"
			job.Status = consts.JobStop
			str, e := res.PersistenceErr(res.NewCmgErr(errcode.InvalidParameter, "File error, please check and upload again"), job.ErrMsg)
			if e != nil {
				l.Errorw("Marshal error", logx.Field("err", e))
			}
			dataMap["err_msg"] = str
		}
		dataMap["status"] = job.Status
		dataMap["progress"] = job.Progress
		svcCtx.Dao.Job.Update(job.ID, dataMap)
	}()
	// 文件类型判断
	header := make([]byte, 4) // 只需要前 4 个字节
	_, err = file.Read(header)
	if err != nil {
		l.Errorw("err reading file", logx.Field("filename", fileHeader.Filename), logx.Field("err", err))
		readerErr = err
		return "", err
	}
	// 如果不是 xlsx 或 xls 格式
	if !bytes.Equal(header, XLSXFileHeader) && !bytes.Equal(header, XLSFileHeader) && !strings.Contains(fileHeader.Filename, ".csv") {
		l.Errorw("illegal file format", logx.Field("filename", fileHeader.Filename))
		err = res.NewCmgErr(errcode.InvalidParameter, "Invalid file")
		readerErr = err
		return "", err
	}

	// 检测文件编码格式
	if strings.Contains(fileHeader.Filename, ".csv") {
		ok, err := excel.CheckFileCharset(file)
		if !ok || err != nil {
			l.Errorw("illegal file format", logx.Field("filename", fileHeader.Filename), logx.Field("err", err))
			err = res.NewCmgErr(errcode.InvalidParameter, "Illegal file format")
			readerErr = err
			return "", err
		}
	}

	_, err = file.Seek(0, io.SeekStart) // 将文件读写指针重置到开头
	if err != nil {
		l.Errorw("err seek file", logx.Field("filename", fileHeader.Filename), logx.Field("err", err))

		readerErr = err
		return "", err
	}

	// 生成不重复的文件名
	fileName := randomFileName(randByteLength, fileHeader.Filename)
	if strings.Contains(fileName, "..") {
		err = res.NewCmgErr(errcode.InvalidParameter, "Illegal file path")
		readerErr = err
		return "", err
	}
	// 获取上传的路径
	fullFilePath, _ := utils.GetUploadFilePath(fileName, svcCtx.Config.FileSavePath, uin)
	l.Infof("upload file: %s, file size: %s, MIME header: %+v", fileHeader.Filename, formatFileSize(fileHeader.Size), fileHeader.Header)
	tempFile, err := os.Create(fullFilePath)
	if err != nil {
		l.Errorf("create File error %s", err.Error())
		readerErr = err
		return "", err
	}
	defer func() {
		closeErr := tempFile.Close()
		if closeErr != nil {
			l.Errorw("close tempfile error", logx.Field("err", closeErr))
		}
		if readerErr != nil { //异常或人工终止 删除文件
			rmerr := os.Remove(fullFilePath)
			if rmerr != nil {
				l.Errorw("remove file error", logx.Field("err", rmerr))
			}
		}

	}()
	// 创建进度信息
	progress := &UploadProgress{
		TotalBytes:    fileHeader.Size,
		UploadedBytes: 0,
		Ctx:           ctx,
		Job:           job,
	}

	// 创建ProgressReader
	progressReader := &ProgressReader{
		reader:   file,
		progress: progress,
	}
	// 文件内容复制
	_, err = io.Copy(tempFile, progressReader)
	if err != nil {
		l.Errorf("copy file error %s", err.Error())
		if errors.Is(err, context.Canceled) {
			readerErr = res.NewCmgErr(errcode.BizError, "Manual Termination")
		} else {
			readerErr = err
		}
		return "", err
	}
	// xls 转 xlsx(阿里云导出为 xls 格式)
	if bytes.Equal(header, XLSFileHeader) {
		fullFileXLSXPath := strings.TrimSuffix(fullFilePath, filepath.Ext(fullFilePath)) + ".xlsx"
		err = convertXLS2XLSX(fullFilePath, fullFileXLSXPath, l)
		if err != nil {
			l.Error("xlx 2 xlsx fail", err)
			return "", res.NewCmgErr(errcode.InternalError, "xlx 2 xlsx fail")
		}
		fullFilePath = fullFileXLSXPath
	}

	return fullFilePath, nil
}

// randomFileName
func randomFileName(length int, filename string) string {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return ""
	}
	timestamp := time.Now().Format("20060102150405")
	fileArr, fileSuffix := SplitLast(filename, ".")
	return fileArr + "_" + timestamp + "_" + hex.EncodeToString(bytes) + "." + fileSuffix
}

func SplitLast(s, sep string) (string, string) {
	idx := strings.LastIndex(s, sep)
	if idx == -1 {
		return s, "" // 如果分隔符不存在，返回原字符串和空字符串
	}
	return s[:idx], s[idx+len(sep):]
}

// JobAnalysis function do
func JobAnalysis(ctx context.Context, l logx.Logger, svcCtx *svc.ServiceContext,
	fullFilePath string, fileName string, job *dao.Job, isFixData bool) error {
	var jobErr *res.CmgErr
	l.Infof("start %s fileName %s job analysis", job.JobType, fullFilePath)
	files := make(map[string]struct{}) //清理文件清单

	todoContext := context.TODO()
	todoContext = logc.InjectStaffInfo(todoContext, job.Uin, job.SubAccountUin)

	defer func() {
		if jobErr != nil && !jobErr.IsEmpty() {
			l.Errorw("process job error", logx.Field("jobInfo", job), logx.Field("jobErr", jobErr))
			job.Status = consts.JobStop
			dataMap := make(map[string]interface{})
			dataMap["status"] = consts.JobStop
			dataMap["ak_hash"] = job.AkHash
			dataMap["progress"] = 100
			str, e := res.PersistenceErr(jobErr, "")
			if e != nil {
				l.Errorw("Marshal error", logx.Field("err", e))
			}
			dataMap["err_msg"] = str

			svcCtx.Dao.Job.Update(job.ID, dataMap)
			wechat_sdk.SendWechatMsg(
				fmt.Sprintf("导入失败告警：UIN[%s],SubUin[%s] 通过 [%s] 导入调研数据在阶段【解析 xlsx】出错, jobID[%s] ,源端[%s], 名称 [%s] ,部门[%s],中文名 [%s],产品信息[%s], 错误信息[%s]",
					job.Uin, job.SubAccountUin, job.JobType, job.JobID, job.Src, logc.GetRtxName(todoContext),
					logc.GetDeptName(todoContext), logc.GetChineseName(todoContext), job.NormalMsg, str), false)
		}
		//清理本次任务产生的文件
		for filePath, _ := range files {
			rmerr := os.Remove(filePath)
			if rmerr != nil {
				l.Errorw("remove file error", logx.Field("err", rmerr))
			}
		}
	}()
	file, err := os.Open(fullFilePath)
	if err != nil {
		l.Errorw("analysisFile open file error", logx.Field("err", err.Error()))
		jobErr = res.NewCmgErrMsg("analysisFile open file error")
		return jobErr
	}
	files[fullFilePath] = struct{}{}
	defer file.Close()

	buffer := make([]byte, 4)
	_, err = io.ReadFull(file, buffer)
	if job.Src == string(platform.Idc) && !isFixData {
		switch job.JobType {
		case consts.ToolScanImport:
			if !bytes.Equal(buffer, XLSXFileHeader) {
				l.Errorw("analysisFile illegal file format", logx.Field("filename", fileName))
				jobErr = res.NewCmgErr(errcode.InvalidParameter, "analysisFile illegal file format")
				return jobErr
			}
			err = utils.DelExcelRows(fullFilePath, 1, 1)
			if err != nil {
				jobErr = res.NewCmgErr(errcode.InvalidParameter, "analysisFile illegal file format")
				return jobErr
			}
		case consts.Import:
			if !strings.Contains(fileName, handlerConsts.CSVSuffix) || (buffer[0] != 0xEF || buffer[1] != 0xBB || buffer[2] != 0xBF) {
				l.Errorw("analysisFile illegal file format", logx.Field("filename", fileName))
				jobErr = res.NewCmgErr(errcode.InvalidParameter, "csv trans to xlsx failed")
				return jobErr
			}
			fullFilePath, err = utils.ConvertCSV2XLSX(fullFilePath, idcServer.SheetName, "")
			l.Infow("analysisFile convert csv to xlsx:", logx.Field("localPath", fullFilePath))
			if err != nil {
				jobErr = res.NewCmgErr(errcode.InvalidParameter, "csv trans to xlsx failed")
				return jobErr
			}
		}
	}
	//if job.Src == string(platform.Aws) && !isFixData {
	//	switch job.JobType {
	//	case consts.ExpenseImport:
	//		if !strings.Contains(fileName, handlerConsts.CSVSuffix) {
	//			l.Errorw("analysisFile illegal file format", logx.Field("filename", fileName))
	//			jobErr = res.NewCmgErr(errcode.InvalidParameter, "csv trans to xlsx failed")
	//			return jobErr
	//		}
	//		fullFilePath, err = utils.ConvertCSV2XLSX(fullFilePath, aws.AwsBillSheetName, "")
	//		l.Infow("analysis aws bill file convert csv to xlsx:", logx.Field("localPath", fullFilePath))
	//		if err != nil {
	//			l.Errorw("filter aws bill info failed", logx.Field("err", err))
	//			jobErr = res.NewCmgErr(errcode.InvalidParameter, "csv trans to xlsx failed")
	//			return jobErr
	//		}
	//	}
	//}
	//if job.Src == string(platform.Aliyun) && !isFixData {
	//	switch job.JobType {
	//	case consts.ExpenseImport:
	//		if !strings.Contains(fileName, handlerConsts.CSVSuffix) {
	//			l.Errorw("analysisFile illegal file format", logx.Field("filename", fileName))
	//			jobErr = res.NewCmgErr(errcode.InvalidParameter, "csv trans to xlsx failed")
	//			return jobErr
	//		}
	//		fullFilePath, err = utils.ConvertCSV2XLSX(fullFilePath, aliyun.AliyunBillSheetName, "")
	//		l.Infow("analysis aliyun bill file convert csv to xlsx:", logx.Field("localPath", fullFilePath))
	//		if err != nil {
	//			l.Errorw("filter aliyun bill info failed", logx.Field("err", err))
	//			jobErr = res.NewCmgErr(errcode.InvalidParameter, "csv trans to xlsx failed")
	//			return jobErr
	//		}
	//	}
	//}
	files[fullFilePath] = struct{}{}
	dataListHandler, jobErr := register.ParseXlsx(ctx, fullFilePath, job, l, svcCtx.Config, isFixData)
	if jobErr != nil && !jobErr.IsEmpty() {
		l.Errorw("ParseXlsx failed", logx.Field("err", err))
		if errors.Is(err, context.Canceled) {
			jobErr = res.NewCmgErr(errcode.BizError, "Manual Termination")
		} else if errors.Is(err, register.ErrPlatformError) {
			jobErr = res.NewCmgErr(errcode.InvalidParameter,
				"File exception:Not a valid %s data file, please check and upload again",
				consts.CloudName[job.Src])
		} else if errors.Is(err, register.VpcNoUserIDError) {
			jobErr = res.NewCmgErr(errcode.InvalidParameter,
				"Import failed. Please add the user ID in the VPC and upload again")
		} else if errors.Is(err, register.NoVpcSheetError) {
			jobErr = res.NewCmgErr(errcode.InvalidParameter,
				"The Vpc Sheet is missing in the import file. Please add it and upload it again")
		} else {
			return jobErr
		}
	}

	job.Progress = 95
	//读取excel结束后尽快补上信息给到前端查询
	dataMap := make(map[string]interface{})
	dataMap["progress"] = job.Progress
	if job.AkHash != "" {
		dataMap["ak_hash"] = job.AkHash
	}
	if job.JobType == consts.Import || job.JobType == consts.ToolScanImport || job.JobType == consts.ExpenseImport {
		dataMap["analysis_at"] = job.AnalysisAt
	}
	err = svcCtx.Dao.Job.Update(job.ID, dataMap)
	if err != nil {
		job.ErrMsg = "Batch information update failed"
		jobErr = res.NewCmgErrMsg("Failed to write data")
		l.Error("JobAnalysis: failed to update job", logx.Field("err", err))
		return jobErr
	}

	count := 0
	for dataListHandler.HasNext() {
		dataList, err := dataListHandler.GetCurrentPage()
		if err != nil {
			l.Errorw("GetCurrentPage failed", logx.Field("err", err))
			return res.NewCmgErrMsg(err.Error())
		}
		err = batchSaveDataList(ctx, l, svcCtx, job, dataList, isFixData)
		if err != nil {
			return res.NewCmgErrMsg(err.Error())
		}

		if dataList == nil {
			break
		}
		count++
	}

	// 换成  批量解析过后再统一保存
	if count == 0 { // 没有数据，直接结束
		job.Status = consts.JobStop
		job.NormalMsg = ""
		job.ErrMsg = "No valid data"
		jobErr = res.NewCmgErrMsg("No valid data")
		return jobErr
	}

	dataMap["progress"] = 100
	dataMap["status"] = job.Status
	dataMap["normal_msg"] = job.NormalMsg

	if "" != job.ErrMsg {
		str, e := res.PersistenceErr(res.NewCmgErr(errcode.BizError, job.ErrMsg), job.ErrMsg)
		if e != nil {
			l.Errorw("Marshal error", logx.Field("err", e))
		}
		dataMap["err_msg"] = str
	} else {
		dataMap["err_msg"] = job.ErrMsg
	}

	err = svcCtx.Dao.Job.Update(job.ID, dataMap)
	if err != nil {
		job.ErrMsg = "Batch information update failed"
		l.Error("ImportUrpJob: failed to add job", logx.Field("err", err))
		jobErr = res.NewCmgErrMsg(job.ErrMsg)
		return jobErr
	}

	// 数据转正

	wechat_sdk.SendWechatMsg(
		fmt.Sprintf("UIN[%s],SubUin[%s] 通过 [%s] 导入调研数据, jobID[%s] ,源端[%s], 名称 [%s] ,部门[%s],中文名 [%s],产品信息[%s], 错误信息[%s]",
			job.Uin, job.SubAccountUin, job.JobType, job.JobID, job.Src, logc.GetRtxName(todoContext),
			logc.GetDeptName(todoContext), logc.GetChineseName(todoContext), job.NormalMsg, job.ErrMsg), false)
	l.Infow("文件导入成功", logx.Field("jobID", job.JobID), logx.Field("src", job.Src), logx.Field("jobType", job.JobType), logx.Field("NormalMsg", job.NormalMsg))
	return nil
}

// batchSaveDataList 从上面的方法中剥离出这段保存数据库的逻辑 ，方便巨大datalist的时候能分批执行 ,此内部逻辑无改动照搬
func batchSaveDataList(ctx context.Context, l logx.Logger, svcCtx *svc.ServiceContext, job *dao.Job, dataList []*dao.Data, isFixData bool) error {
	dbErr := svcCtx.Dao.DB.Transaction(func(tx *gorm.DB) error {
		// 解析状态
		newD := dao.NewWithDB(tx)

		if (job.JobType == consts.Import || job.JobType == consts.MigrateCenter || job.JobType == consts.ToolScanImport || job.JobType == consts.ExpenseImport) && !isFixData {
			isMaxAnalysis, err := newD.Job.IsMaxAnalysis(job.Uin, job.Src, job.AkHash, job.AnalysisAt)
			if err != nil {
				l.Errorw("IsMaxAnalysis count fail", logx.Field("err", err))
				job.ErrMsg = "Comparison analysis time failed"
				return res.NewCmgErrMsg(job.ErrMsg)
			}
			l.Infof("IsMaxAnalysis %s|%s|%s|%d|%v", job.Uin, job.Src, job.AkHash, job.AnalysisAt, isMaxAnalysis)
			newUrpDataList, err := newD.Data.BatchSaveScanInfo(ctx, isMaxAnalysis, dataList, job.AnalysisAt)
			if err != nil {
				l.Errorw("BatchSaveScanInfo: failed to batch import data", logx.Field("err", err))
				if errors.Is(err, context.Canceled) {
					job.ErrMsg = "Manual Termination"
				} else {
					job.ErrMsg = "Data storage failed"
				}
				return res.NewCmgErrMsg(job.ErrMsg)
			}
			l.Infow("newUrpDataList’s size", logx.Field("size", len(newUrpDataList)))
			err = register.ProcessDataTag(ctx, newD, job, newUrpDataList, len(dataList)-len(newUrpDataList))
			if err != nil {
				l.Errorw("ProcessDataTag fail", logx.Field("err", err))
				if errors.Is(err, context.Canceled) {
					job.ErrMsg = "Manual Termination"
				} else {
					job.ErrMsg = "Tag parsing exception"
				}
				return res.NewCmgErrMsg(job.ErrMsg)
			}

		} else if isFixData {
			//找到所有状态为草稿的数据，只处理这批内容
			newData, err := register.ProcessDraftData(ctx, newD, job, dataList)
			if err != nil {
				l.Errorw("ProcessDraftData failed ", logx.Field("err", err))
				if errors.Is(err, context.Canceled) {
					job.ErrMsg = "Manual Termination"
				} else {
					job.ErrMsg = "Failed to analyze abnormal data"
				}
				return res.NewCmgErrMsg(job.ErrMsg)
			}
			err = newD.Data.BatchSaveFixInfo(ctx, newData)
			if err != nil {
				l.Errorw("BatchSaveFixInfo: failed to batch import data", logx.Field("err", err))
				if errors.Is(err, context.Canceled) {
					job.ErrMsg = "Manual Termination"
				} else {
					job.ErrMsg = "Data storage failed"
				}
				return res.NewCmgErrMsg(job.ErrMsg)
			}
		}
		return nil
	})
	return dbErr
}

func formatFileSize(size int64) string {
	if size < KB {
		return fmt.Sprintf("%d B", size)
	} else if size < MB {
		return fmt.Sprintf("%.2f KB", float64(size)/KB)
	} else if size < GB {
		return fmt.Sprintf("%.2f MB", float64(size)/MB)
	} else {
		return fmt.Sprintf("%.2f GB", float64(size)/GB)
	}
}

func convertXLS2XLSX(inputFile string, outputFile string, l logx.Logger) error {
	xlsFile, err := xls.OpenFile(inputFile)
	if err != nil {
		l.Error("open failed: ", err)
		return err
	}
	// 创建一个新的 XLSX 文件
	xlsxFile := excelize.NewFile()
	// 遍历 XLS 文件中的每个 sheet
	for sheetIndex := 0; sheetIndex < xlsFile.GetNumberSheets(); sheetIndex++ {
		sheet, _ := xlsFile.GetSheet(sheetIndex)
		// 添加一个新的 sheet 到 XLSX 文件中
		sheetName := sheet.GetName()
		_, err = xlsxFile.NewSheet(sheetName)
		if err != nil {
			return err
		}
		rowNumber := sheet.GetNumberRows()
		// 遍历 XLS 文件中的每一行  GetNumberRows每次都会重新计算 ； xls特定情况下会空转累加返回64k(不明确)  ..
		for rowIndex := 0; rowIndex < rowNumber; rowIndex++ {
			row, _ := sheet.GetRow(rowIndex)
			count := 0
			colNumber := len(row.GetCols())
			// 遍历 XLS 文件的每一个单元格
			for colIndex := 0; colIndex < colNumber; colIndex++ {
				value, _ := row.GetCol(colIndex)
				// 将 XLS 单元格数据写入 XLSX 文件中
				axis, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+1)

				err = xlsxFile.SetCellValue(sheetName, axis, value.GetString())
				if err != nil {
					return err
				}
				if len(value.GetString()) == 0 {
					count++
				}
			}
			// 代码层面兼容 处理的文件中的空行（当一行中的所有列数据都是空时，跳过）
			if count == colNumber {
				break
			}
		}
	}

	if xlsxFile.SheetCount > 1 {
		_ = xlsxFile.DeleteSheet("Sheet1")
	}

	// 保存 XLSX 文
	if err = xlsxFile.SaveAs(outputFile); err != nil {
		l.Error("Error while saving the XSLX file:", err)
		return err
	}
	return nil
}
